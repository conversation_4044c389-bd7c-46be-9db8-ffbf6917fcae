import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
// Use relative path to avoid aliasing issues
import prisma from "./prisma";
import { AUTH_GITHUB_CLIENT_ID, AUTH_GITHUB_CLIENT_SECRET } from "$env/static/private";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql"
	}),
	socialProviders: {
		github: {
			clientId: AUTH_GITHUB_CLIENT_ID,
			clientSecret: AUTH_GITHUB_CLIENT_SECRET
		}
	}
});
