<script lang="ts">
	import ArrowLeft from "@lucide/svelte/icons/arrow-left";
	import { Button } from "$lib/components/ui/button";
	import LogoIcon from "$lib/components/brand/LogoIcon.svelte";

	let { children } = $props();
</script>

<div class="relative flex min-h-svh flex-col items-center justify-center">
	<div class="flex w-full max-w-sm flex-col gap-6">
		<a href="/" aria-label="Home" class="flex items-center gap-2 self-center font-medium">
			<LogoIcon class="text-logo bg-foreground size-7.5 rounded-md p-0.5" />
			EvoprofLMS
		</a>

		{@render children()}

		<div class="text-muted-foreground text-center text-xs text-balance">
			By clicking continue, you agree to our <span
				class="hover:text-primary underline hover:cursor-pointer">Terms of Service</span
			>{""} and
			<span class="hover:text-primary underline hover:cursor-pointer">Privacy Policy</span>
		</div>
	</div>
</div>

<Button href="/" aria-label="Go Back" variant="outline" class="absolute top-4 left-4"
	><ArrowLeft class="size-4" />Back</Button
>
