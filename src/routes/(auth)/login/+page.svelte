<script>
	import { <PERSON><PERSON> } from "$lib/components/ui/button";
	import * as Card from "$lib/components/ui/card";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import GithubIcon from "@lucide/svelte/icons/github";
</script>

<Card.Root>
	<Card.Header>
		<Card.Title>Welcome Back!</Card.Title>
		<Card.Description>Login with your github email account</Card.Description>
	</Card.Header>
	<Card.Content class="flex flex-col gap-4">
		<!-- Github Button -->
		<Button class="w-full" variant="outline">
			<GithubIcon class="size-4" />
			Sign in with Github
		</Button>

		<!-- Divider -->
		<div
			class="after:border-border relative text-center text-sm after:absolute after:inset-0 after:bottom-1/2 after:z-0 after:flex after:items-center after:border-b"
		>
			<span class="bg-card text-muted-foreground relative z-10 px-2">Or continue with</span>
		</div>

		<div class="grid gap-3">
			<div class="grid gap-2">
				<Label>Email</Label>
				<Input type="email" placeholder="<EMAIL>"/>
			</div>

			<Button>Continue with Email</Button>
		</div>
	</Card.Content>
</Card.Root>
