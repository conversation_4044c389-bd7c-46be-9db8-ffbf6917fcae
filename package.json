{"name": "digital-profiles-lms", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "bits-ui": "^2.8.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "layerchart": "2.0.0-next.23", "mode-watcher": "^1.0.8", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.10.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vaul-svelte": "1.0.0-next.7", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@prisma/client": "^6.10.0", "better-auth": "^1.2.9", "zod": "^3.25.67"}}